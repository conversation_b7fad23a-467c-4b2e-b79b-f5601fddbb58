<script setup>
import { ref, computed, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import { usePublicBitmapRequest } from '../composables/usePublicBitmapRequest';

const route = useRoute();
const { data: bitmapData, isLoading, error, fetchData } = usePublicBitmapRequest();
const highlight = ref(null);

const formatDescription = (text) => {
  if (!text) {return '';}
  const lines = text.replace(/\r\n/g, '\n').split('\n');
  return lines.map(line => {
    if (/^\d+\.\s/.test(line)) {
      return `<div class='description-step'>${line}</div>`;
    }
    return `<div class='description-paragraph'>${line}</div>`;
  }).join('');
};

const fetchHighlight = async () => {
  await fetchData(1, '', '', '', '', '');
  if (bitmapData.value?.data) {
    const found = bitmapData.value.data.find(item => String(item.AdsId) === String(route.params.id));
    if (found) {
      highlight.value = {
        id: found.AdsId,
        title: found.Subject,
        description: found.Descp,
        image: found.ImgUrl,
        date: found.AdsDate,
      };
    }
  }
};

onMounted(fetchHighlight);
</script>

<template>
  <div class="highlight-detail-section">
    <div v-if="isLoading" class="loading">Loading...</div>
    <div v-else-if="error" class="error">Error loading highlight.</div>
    <div v-else-if="highlight" class="highlight-detail-card">
      <img :src="highlight.image" :alt="highlight.title" class="highlight-detail-image" />
      <h2 class="highlight-detail-title">{{ highlight.title }}</h2>
      <div class="highlight-detail-description" v-html="formatDescription(highlight.description)"></div>
      <div class="highlight-detail-date">{{ new Date(highlight.date).toLocaleDateString() }}</div>
    </div>
    <div v-else class="not-found">Highlight not found.</div>
  </div>
</template>

<style scoped>
.highlight-detail-section {
  max-width: 600px;
  margin: 2rem auto;
  padding: 1.5rem;
  background: var(--card-background, #222);
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.08);
}
.back-btn {
  margin-bottom: 1rem;
  background: var(--primary-color, #007bff);
  color: #fff;
  border: none;
  border-radius: 4px;
  padding: 0.5rem 1rem;
  cursor: pointer;
}
.highlight-detail-card {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.highlight-detail-image {
  width: 100%;
  max-width: 400px;
  border-radius: 8px;
  margin-bottom: 1rem;
  object-fit: contain;
}
.highlight-detail-title {
  font-size: 2rem;
  margin-bottom: 1rem;
  color: var(--text-color, #fff);
  text-align: center;
}
.highlight-detail-description {
  font-size: 1rem;
  color: var(--text-light, #ccc);
  margin-bottom: 1rem;
  line-height: 1.6;
}
.highlight-detail-description :deep(.description-step) {
  margin: 0.5rem 0;
  padding-left: 1rem;
  position: relative;
}
.highlight-detail-description :deep(.description-paragraph) {
  margin: 0.5rem 0;
}
.highlight-detail-description :deep(.description-step)::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0.5rem;
  width: 4px;
  height: 4px;
  background-color: var(--primary-color, #007bff);
  border-radius: 50%;
}
.highlight-detail-date {
  font-size: 0.9rem;
  color: var(--text-light, #aaa);
  text-align: right;
}
.loading, .error, .not-found {
  text-align: center;
  color: var(--text-light, #aaa);
  margin: 2rem 0;
}
.home-image-card {
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;
  border-radius: 0 !important;
  margin-right: 1rem;
  padding: 0 !important;
  overflow: visible;
  height: 320px;
  min-height: 220px;
  max-height: none;
  display: flex;
  align-items: stretch;
}
.home-image-content,
.highlight-image-container.home-full-image {
  width: 100%;
  height: 100%;
  padding: 0 !important;
  margin: 0 !important;
  background: transparent !important;
  border-radius: 0 !important;
  box-shadow: none !important;
  display: flex;
  align-items: center;
  justify-content: center;
}
.highlight-image.home-full-image {
  object-fit: cover;
  width: 100%;
  height: 100%;
  border-radius: 0 !important;
  background: transparent !important;
  max-width: 100vw;
  max-height: 100%;
}
@media (min-width: 769px) {
  .home-image-card {
    height: 400px;
    max-height: 500px;
  }
}
</style> 